import enum
import uuid

from database.core import Base
from sqlalchemy import (
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Index,
    Integer,
    Text,
    UniqueConstraint,
    func,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship


class StepStatusEnum(enum.Enum):
    PENDING = "pending"
    SUCCESSFUL = "successful"
    FAILED = "failed"


class RunbookTypeEnum(enum.Enum):
    TROUBLESHOOT = "troubleshoot"
    ROLLBACK = "rollback"
    MITIGATION = "mitigation"
    RECOVERY = "recovery"
    OTHER = "other"


class Runbook(Base):
    __tablename__ = "runbooks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    incident_id = Column(
        UUID(as_uuid=True),
        ForeignKey("incidents.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    title = Column(Text, nullable=False)
    type = Column(Enum(RunbookTypeEnum), nullable=False)
    purpose = Column(Text, nullable=True)
    details = Column(Text, nullable=True)
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Relationships
    incident = relationship("Incident", back_populates="runbooks")
    steps = relationship(
        "RunbookStep",
        back_populates="runbook",
        cascade="all, delete-orphan",
        order_by="RunbookStep.step_order",
    )

    def __repr__(self):
        return f"<Runbook(id='{self.id}', incident_id='{self.incident_id}', title='{self.title}')>"


class RunbookStep(Base):
    __tablename__ = "runbook_steps"
    __table_args__ = (
        UniqueConstraint("runbook_id", "step_order", name="runbook_steps_runbook_id_step_order_key"),
        Index("ix_runbook_id", "runbook_id"),
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    runbook_id = Column(
        UUID(as_uuid=True),
        ForeignKey("runbooks.id", ondelete="CASCADE"),
        nullable=False,
    )
    step_order = Column(Integer, nullable=False)
    title = Column(Text, nullable=False)
    description = Column(Text, nullable=False)
    details = Column(Text, nullable=False)
    expected_result = Column(Text, nullable=False)
    status = Column(
        Enum(StepStatusEnum), nullable=False, default=StepStatusEnum.PENDING
    )
    notes = Column(Text, nullable=True)
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )
    executed_at = Column(DateTime(timezone=True), nullable=True)
    executed_by = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True
    )
    executed_by_user = relationship("User", back_populates="executed_steps")
    runbook = relationship("Runbook", back_populates="steps")

    def __repr__(self):
        return f"<RunbookStep(id='{self.id}', runbook_id='{self.runbook_id}', step_order='{self.step_order}')>"
